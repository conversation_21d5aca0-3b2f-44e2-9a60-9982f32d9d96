#ifndef PROTOCOL_SERVICE_HPP
#define PROTOCOL_SERVICE_HPP

#include <atomic>
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <queue>
#include <vector>

#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/base/observer.hpp"
#include "zexuan/base/subject.hpp"
#include "zexuan/bus/tcp_bus_client.hpp"
#include "zexuan/net/event_loop_thread.hpp"

namespace zexuan {
  namespace protocol {

    // 前向声明
    namespace transform {
      class ProtocolTransform;
    }

    namespace service {

      /**
       * @brief 协议服务类
       * 参考原始的 TNXEcMsgOperationObj 设计
       * 负责业务逻辑处理和消息转换
       */
      class ProtocolService {
      public:
        // 构造函数 - 使用固定ID，每个连接独立的Mediator
        explicit ProtocolService(std::shared_ptr<base::Mediator> mediator,
                                const std::string& bus_host = "127.0.0.1",
                                uint16_t bus_port = 8081);
        virtual ~ProtocolService();

        // 生命周期管理
        bool Initialize();
        bool Start();
        bool Stop();
        bool IsRunning() const { return is_running_; }

        // 设置Transform（用于协议转换）
        void SetProtocolTransform(std::unique_ptr<transform::ProtocolTransform> transform);

        // 事件通过Subject的SendEventNotify方法发送，无需单独的回调设置

      private:
        // 基础成员
        std::shared_ptr<base::Mediator> mediator_;

        // Observer和Subject（参考原始设计）
        std::shared_ptr<base::Observer> observer_;
        std::shared_ptr<base::Subject> subject_;

        // 协议转换器
        std::unique_ptr<transform::ProtocolTransform> protocol_transform_;

        // TCP消息总线客户端 - 使用专门的线程管理
        std::unique_ptr<zexuan::net::EventLoopThread> bus_event_loop_thread_;
        std::shared_ptr<zexuan::bus::TcpBusClient> bus_client_;
        std::string bus_host_;
        uint16_t bus_port_;

        // 状态管理
        std::atomic<bool> is_initialized_{false};
        std::atomic<bool> is_running_{false};
        std::atomic<bool> should_stop_{false};

        // 线程管理（业务处理线程已移除）

        // 自动事件上报功能已移植到tcp_bus_client

        // 消息队列已移除，消息直接转发到总线

        // 事件通过Subject发送，无需回调成员变量

        // 消息处理回调
        void OnCommonMessage(const base::CommonMessage& message);
        void OnEventMessage(const base::EventMessage& message);

        // 总线消息处理回调
        void OnBusCommonMessage(const zexuan::base::CommonMessage& message);
        void OnBusEventMessage(const zexuan::base::EventMessage& message);
        void OnBusControlMessage(const base::ControlMessage& message);

        // 总线相关方法
        bool InitializeBusClient();
        void StopBusClient();

        // 事件上报和业务处理功能已移植到tcp_bus_client

        // 业务逻辑处理已移植到tcp_bus_client，这里不再需要

        // 工具方法（如果需要的话可以添加）
      };

    }  // namespace service
  }  // namespace protocol
}  // namespace zexuan

#endif  // PROTOCOL_SERVICE_HPP