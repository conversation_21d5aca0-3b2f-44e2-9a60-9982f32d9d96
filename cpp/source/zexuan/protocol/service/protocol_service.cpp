#include "zexuan/protocol/service/protocol_service.hpp"

#include <spdlog/spdlog.h>
#include <chrono>
#include <thread>

#include "zexuan/protocol/transform/protocol_transform.hpp"

namespace zexuan {
  namespace protocol {
    namespace service {

      ProtocolService::ProtocolService(std::shared_ptr<base::Mediator> mediator,
                                       const std::string& bus_host,
                                       uint16_t bus_port)
          : mediator_(mediator), bus_host_(bus_host), bus_port_(bus_port) {
        // 创建Observer - 使用固定ID接收来自Gateway的命令
        observer_ = std::make_shared<base::Observer>(base::SERVICE_OBSERVER_ID, mediator_);

        // 设置Observer的事件回调 - 处理来自Gateway的EventMessage
        observer_->SetEventCallback(
            [this](const base::EventMessage& message) -> base::Result<void> {
              OnEventMessage(message);
              return base::Result<void>{};
            });

        // 创建Subject - 使用固定ID向Gateway发送响应和事件
        subject_ = std::make_shared<base::Subject>(base::SERVICE_SUBJECT_ID, mediator_);

        // 设置Subject的命令回调 - 处理来自Gateway的命令
        subject_->SetCmdCallback([this](const base::CommonMessage& message) -> base::Result<void> {
          OnCommonMessage(message);
          return base::Result<void>{};
        });

        spdlog::info("ProtocolService created with Observer ID: {}, Subject ID: {}, Bus: {}:{}",
                     base::SERVICE_OBSERVER_ID, base::SERVICE_SUBJECT_ID, bus_host_, bus_port_);
      }

      ProtocolService::~ProtocolService() {
        Stop();

        // 停止总线客户端
        StopBusClient();

        spdlog::info("ProtocolService destroyed");
      }

      bool ProtocolService::Initialize() {
        if (is_initialized_) {
          return true;
        }

        if (!mediator_) {
          spdlog::error("Mediator is null");
          return false;
        }

        // 初始化Observer（包含注册到Mediator）
        auto observer_result = observer_->Init();
        if (!observer_result) {
          spdlog::error("Failed to initialize observer: {}",
                        static_cast<int>(observer_result.error()));
          return false;
        }

        // 初始化Subject（包含注册到Mediator）
        auto subject_result = subject_->Init();
        if (!subject_result) {
          spdlog::error("Failed to initialize subject: {}",
                        static_cast<int>(subject_result.error()));
          return false;
        }

        is_initialized_ = true;
        spdlog::info("ProtocolService initialized");
        return true;
      }

      bool ProtocolService::Start() {
        if (!is_initialized_) {
          spdlog::error("Service not initialized");
          return false;
        }

        if (is_running_) {
          return true;
        }

        should_stop_ = false;

        // 在独立线程中初始化总线客户端
        if (!InitializeBusClient()) {
          spdlog::error("Failed to initialize bus client");
          return false;
        }

        is_running_ = true;
        spdlog::info("ProtocolService started with bus client");
        return true;
      }

      bool ProtocolService::Stop() {
        if (!is_running_) {
          return true;
        }

        should_stop_ = true;

        // 所有线程管理已移除

        // 注销 Observer 和 Subject 以打破循环引用
        if (observer_) {
          auto result = observer_->Exit();
          if (!result) {
            spdlog::warn("Failed to unregister observer: {}", static_cast<int>(result.error()));
          }
        }

        if (subject_) {
          auto result = subject_->Exit();
          if (!result) {
            spdlog::warn("Failed to unregister subject: {}", static_cast<int>(result.error()));
          }
        }

        is_running_ = false;
        spdlog::info("ProtocolService stopped");
        return true;
      }

      void ProtocolService::SetProtocolTransform(
          std::unique_ptr<transform::ProtocolTransform> transform) {
        protocol_transform_ = std::move(transform);
        spdlog::info("ProtocolService set protocol transform");
      }

      // 事件通过Subject的SendEventNotify方法发送，无需单独的回调设置方法

      // 消息处理回调
      void ProtocolService::OnCommonMessage(const base::CommonMessage& message) {
        spdlog::debug("Service received CommonMessage from {}, original target_id={}", message.source_id, message.target_id);

        // 直接转发到总线，让tcp_bus_client处理和生成响应
        if (bus_client_ && bus_client_->isConnected()) {
          // 创建转发消息，设置正确的source_id和target_id
          zexuan::base::CommonMessage forward_msg = message;
          forward_msg.source_id = base::SERVICE_SUBJECT_ID;  // 设置为自己的ID以便接收回复
          // 保持原始消息的target_id，这样能正确路由到目标tcp_bus_client
          spdlog::debug("ProtocolService: Before forwarding - target_id={}", forward_msg.target_id);

          if (bus_client_->sendCommonMessage(forward_msg)) {
            spdlog::debug("ProtocolService: Forwarded CommonMessage to bus for processing (target_id={})", forward_msg.target_id);
          } else {
            spdlog::error("ProtocolService: Failed to forward CommonMessage to bus");
          }
        } else {
          spdlog::error("ProtocolService: Bus client not connected, cannot forward message");
        }
      }

      void ProtocolService::OnEventMessage(const base::EventMessage& message) {
        spdlog::debug("Service received EventMessage");

        // 暂时空实现
        // TODO: 处理事件消息
      }

      // 业务处理线程已移除，消息直接转发到总线

      // 业务消息处理已移植到tcp_bus_client

      // 所有响应生成方法已移植到tcp_bus_client

      // 移除所有响应生成和队列管理方法


      // 事件上报功能已移植到tcp_bus_client

      // 总线相关方法实现
      bool ProtocolService::InitializeBusClient() {
        try {
          // 创建专门的EventLoop线程来管理总线客户端
          bus_event_loop_thread_ = std::make_unique<zexuan::net::EventLoopThread>(
              nullptr, "ProtocolService-Bus");

          // 启动EventLoop线程并获取EventLoop指针
          zexuan::net::EventLoop* event_loop = bus_event_loop_thread_->startLoop();
          if (!event_loop) {
            spdlog::error("Failed to start bus EventLoop thread");
            return false;
          }

          // 创建总线客户端
          bus_client_ = std::make_shared<zexuan::bus::TcpBusClient>(
              event_loop, bus_host_, bus_port_, "ProtocolService");

          // 设置总线消息回调
          bus_client_->setCommonMessageCallback(
              [this](const zexuan::base::CommonMessage& msg) { OnBusCommonMessage(msg); });

          bus_client_->setEventMessageCallback(
              [this](const zexuan::base::EventMessage& msg) { OnBusEventMessage(msg); });

          bus_client_->setControlMessageCallback(
              [this](const base::ControlMessage& msg) { OnBusControlMessage(msg); });

          // 连接到总线
          bus_client_->connect();

          // 等待连接建立
          for (int i = 0; i < 50 && !bus_client_->isConnected(); ++i) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
          }

          if (bus_client_->isConnected()) {
            // 订阅所有消息类型和事件类型，并注册自己的client_id
            std::vector<int> message_types;
            std::vector<int> event_types;

            // 订阅所有CommonMessage类型 (假设类型1-10)
            for (int i = 1; i <= 10; ++i) {
              message_types.push_back(i);
            }

            // 订阅统一的事件类型1
            event_types.push_back(1);  // 统一的事件类型

            // 使用SERVICE_SUBJECT_ID作为client_id进行订阅，以便能够接收回复
            if (bus_client_->subscribe(message_types, event_types, base::SERVICE_SUBJECT_ID)) {
              spdlog::info("ProtocolService: Successfully subscribed to all message types on bus with client_id: {}", base::SERVICE_SUBJECT_ID);
              return true;
            } else {
              spdlog::error("ProtocolService: Failed to subscribe to message types on bus");
              return false;
            }
          } else {
            spdlog::error("ProtocolService: Failed to connect to message bus at {}:{}", bus_host_, bus_port_);
            return false;
          }

        } catch (const std::exception& e) {
          spdlog::error("ProtocolService: Exception initializing bus client: {}", e.what());
          return false;
        }
      }

      void ProtocolService::StopBusClient() {
        if (!bus_event_loop_thread_ && !bus_client_) {
          return;  // 已经清理过了
        }

        spdlog::info("ProtocolService: Starting bus client shutdown...");

        // 首先断开连接，但保持对象存在
        if (bus_client_) {
          spdlog::debug("ProtocolService: Disconnecting bus client");
          bus_client_->disconnect();
        }

        // 停止EventLoop线程，这会等待线程完全结束
        // EventLoopThread的析构函数会调用loop->quit()并join线程
        if (bus_event_loop_thread_) {
          spdlog::debug("ProtocolService: Stopping EventLoop thread");
          bus_event_loop_thread_.reset();
          spdlog::debug("ProtocolService: EventLoop thread stopped");
        }

        // 现在EventLoop线程已经完全停止，可以安全地释放TcpBusClient
        if (bus_client_) {
          spdlog::debug("ProtocolService: Releasing bus client");
          bus_client_.reset();
        }

        spdlog::info("ProtocolService: Bus client stopped and cleaned up");
      }

      // 总线消息处理回调
      void ProtocolService::OnBusCommonMessage(const zexuan::base::CommonMessage& message) {
        spdlog::info("ProtocolService: Received CommonMessage from bus: type={}, source_id={}, invoke_id={}",
                    static_cast<int>(message.type), message.source_id, message.invoke_id);

        // 如果这是响应消息（来自tcp_bus_client），通过protocol_server发送出去
        if (message.type == zexuan::base::MessageType::RESULT && subject_) {
          // 通过Subject发送响应消息
          auto result = subject_->SendResult(message);
          if (result.has_value()) {
            spdlog::debug("ProtocolService: Sent response message through protocol_server");
          } else {
            spdlog::error("ProtocolService: Failed to send response message: error code {}", static_cast<int>(result.error()));
          }
        } else {
          spdlog::debug("ProtocolService: Ignoring non-response message from bus");
        }
      }

      void ProtocolService::OnBusEventMessage(const zexuan::base::EventMessage& message) {
        spdlog::info("ProtocolService: Received EventMessage from bus: event_type={}, source_id={}, description={}",
                    message.event_type, message.source_id, message.description);

        // 将从总线接收到的事件发送给gateway（通过protocol_server）
        if (subject_) {
          auto result = subject_->SendEventNotify(message);
          if (result.has_value()) {
            spdlog::debug("ProtocolService: Sent EventMessage to gateway through protocol_server");
          } else {
            spdlog::error("ProtocolService: Failed to send EventMessage to gateway: error code {}", static_cast<int>(result.error()));
          }
        } else {
          spdlog::error("ProtocolService: No Subject available, EventMessage not sent to gateway");
        }
      }

      void ProtocolService::OnBusControlMessage(const base::ControlMessage& message) {
        spdlog::info("ProtocolService: Received ControlMessage from bus: action={}, success={}, message={}",
                    message.action, message.success, message.message);
      }

    }  // namespace service
  }  // namespace protocol
}  // namespace zexuan