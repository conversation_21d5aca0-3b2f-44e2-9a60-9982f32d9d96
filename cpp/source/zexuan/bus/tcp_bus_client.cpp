/**
 * @file tcp_bus_client.cpp
 * @brief TCP消息总线客户端类实现
 * <AUTHOR>
 * @date 2025-08-24
 */

#include "zexuan/bus/tcp_bus_client.hpp"
#include <spdlog/spdlog.h>
#include "zexuan/net/address.hpp"
#include "nlohmann/json.hpp"

namespace zexuan {
namespace bus {

TcpBusClient::TcpBusClient(zexuan::net::EventLoop* event_loop, 
                           const std::string& server_host, 
                           uint16_t server_port,
                           const std::string& client_name,
                           zexuan::base::ObjectId client_id)
    : event_loop_(event_loop)
    , server_host_(server_host)
    , server_port_(server_port)
    , client_name_(client_name.empty() ? "TcpBusClient" : client_name)
    , client_id_(client_id) {
    
    if (!event_loop_) {
        throw std::invalid_argument("EventLoop cannot be null");
    }
    
    // 创建消息缓冲区
    message_buffer_ = std::make_unique<zexuan::utils::MessageBuffer>();
    
    // 创建TCP客户端
    zexuan::net::Address server_addr(server_host_, server_port_);
    tcp_client_ = std::make_unique<zexuan::net::TcpClient>(event_loop_, server_addr, client_name_);
    
    // 设置回调函数
    tcp_client_->setConnectionCallback(
        [this](const TcpConnectionPtr& conn) { onConnection(conn); });
    
    tcp_client_->setMessageCallback(
        [this](const TcpConnectionPtr& conn, zexuan::net::Buffer* buf, zexuan::net::Timestamp time) {
            onMessage(conn, buf, time);
        });
    
    spdlog::info("TcpBusClient created: {} -> {}:{}", client_name_, server_host_, server_port_);
}

TcpBusClient::~TcpBusClient() {
    disconnect();
}

bool TcpBusClient::connect() {
    if (connected_.load()) {
        spdlog::warn("TcpBusClient {} is already connected", client_name_);
        return true;
    }

    try {
        // 启用TCP客户端的重连行为
        tcp_client_->enableRetry();

        // 异步连接，不等待结果
        tcp_client_->connect();
        spdlog::debug("TcpBusClient {} initiated connection to {}:{}",
                     client_name_, server_host_, server_port_);
        return true;

    } catch (const std::exception& e) {
        spdlog::error("TcpBusClient {} connect exception: {}", client_name_, e.what());
        return false;
    }
}

void TcpBusClient::disconnect() {
    if (!connected_.load()) {
        return;
    }
    
    connected_.store(false);
    
    if (tcp_client_) {
        tcp_client_->disconnect();
    }
    
    spdlog::info("TcpBusClient {} disconnected", client_name_);
}

bool TcpBusClient::isConnected() const {
    return connected_.load();
}

bool TcpBusClient::subscribe(const std::vector<int>& message_types, const std::vector<int>& event_types) {
    return sendSubscriptionMessage(zexuan::base::SubscriptionMessage::Action::SUBSCRIBE, message_types, event_types);
}

bool TcpBusClient::subscribe(const std::vector<int>& message_types, const std::vector<int>& event_types, zexuan::base::ObjectId client_id) {
    // 设置客户端ID
    setClientId(client_id);
    return sendSubscriptionMessage(zexuan::base::SubscriptionMessage::Action::SUBSCRIBE, message_types, event_types);
}

bool TcpBusClient::unsubscribe(const std::vector<int>& message_types, const std::vector<int>& event_types) {
    return sendSubscriptionMessage(zexuan::base::SubscriptionMessage::Action::UNSUBSCRIBE, message_types, event_types);
}

bool TcpBusClient::sendCommonMessage(const zexuan::base::CommonMessage& message) {
    if (!connected_.load()) {
        spdlog::error("TcpBusClient {} not connected", client_name_);
        return false;
    }
    
    std::string json_message = zexuan::utils::MessageSerializer::serializeCommonMessage(message);
    if (json_message.empty()) {
        spdlog::error("Failed to serialize CommonMessage");
        return false;
    }
    
    return sendJsonMessage(json_message);
}

bool TcpBusClient::sendEventMessage(const zexuan::base::EventMessage& message) {
    if (!connected_.load()) {
        spdlog::error("TcpBusClient {} not connected", client_name_);
        return false;
    }
    
    std::string json_message = zexuan::utils::MessageSerializer::serializeEventMessage(message);
    if (json_message.empty()) {
        spdlog::error("Failed to serialize EventMessage");
        return false;
    }
    
    return sendJsonMessage(json_message);
}

std::string TcpBusClient::getServerAddress() const {
    return server_host_ + ":" + std::to_string(server_port_);
}

void TcpBusClient::onConnection(const TcpConnectionPtr& conn) {
    spdlog::debug("TcpBusClient {} onConnection callback called", client_name_);

    if (!conn) {
        spdlog::error("TcpBusClient {} onConnection: conn is null", client_name_);
        return;
    }

    spdlog::debug("TcpBusClient {} connection state: {}", client_name_, conn->connected());

    if (conn->connected()) {
        connected_.store(true);
        spdlog::info("TcpBusClient {} connected to server", client_name_);
    } else {
        connected_.store(false);
        spdlog::info("TcpBusClient {} disconnected from server", client_name_);
    }
}

void TcpBusClient::onMessage(const TcpConnectionPtr& conn,
                            zexuan::net::Buffer* buf,
                            zexuan::net::Timestamp receive_time) {
    if (!conn || !buf) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    // 将接收到的数据添加到缓冲区
    const char* data = buf->peek();
    size_t data_size = buf->readableBytes();
    
    message_buffer_->appendData(reinterpret_cast<const uint8_t*>(data), data_size);
    buf->retrieveAll();
    
    // 尝试提取完整消息
    std::optional<std::string> json_message;
    while ((json_message = message_buffer_->extractMessage()).has_value()) {
        handleJsonMessage(json_message.value());
    }
}

void TcpBusClient::handleJsonMessage(const std::string& json_message) {
    try {
        spdlog::debug("TcpBusClient {} received JSON message: {}", client_name_, json_message.substr(0, 200));

        std::string msg_category = getMessageCategory(json_message);
        spdlog::debug("TcpBusClient {} message category: {}", client_name_, msg_category);

        if (msg_category == "common") {
            auto common_msg = zexuan::utils::MessageSerializer::deserializeCommonMessage(json_message);
            if (common_msg) {
                spdlog::debug("TcpBusClient {} successfully deserialized CommonMessage", client_name_);
                if (common_message_callback_) {
                    spdlog::debug("TcpBusClient {} calling CommonMessage callback", client_name_);
                    common_message_callback_(common_msg.value());
                } else {
                    spdlog::warn("TcpBusClient {} CommonMessage callback not set", client_name_);
                }
            } else {
                spdlog::error("TcpBusClient {} failed to deserialize CommonMessage", client_name_);
            }
        } else if (msg_category == "event") {
            auto event_msg = zexuan::utils::MessageSerializer::deserializeEventMessage(json_message);
            if (event_msg && event_message_callback_) {
                event_message_callback_(event_msg.value());
            }
        } else if (msg_category == "control") {
            auto control_msg = zexuan::utils::MessageSerializer::deserializeControlMessage(json_message);
            if (control_msg && control_message_callback_) {
                control_message_callback_(control_msg.value());
            }
        } else {
            spdlog::warn("TcpBusClient {} received unknown message category: {}", client_name_, msg_category);
        }

    } catch (const std::exception& e) {
        spdlog::error("TcpBusClient {} failed to handle JSON message: {}", client_name_, e.what());
    }
}

bool TcpBusClient::sendSubscriptionMessage(zexuan::base::SubscriptionMessage::Action action,
                                          const std::vector<int>& message_types,
                                          const std::vector<int>& event_types) {
    if (!connected_.load()) {
        spdlog::error("TcpBusClient {} not connected", client_name_);
        return false;
    }

    zexuan::base::SubscriptionMessage sub_msg;
    sub_msg.action = action;
    sub_msg.message_types = message_types;
    sub_msg.event_types = event_types;
    sub_msg.client_id = client_id_;  // 携带客户端ID

    std::string json_message = zexuan::utils::MessageSerializer::serializeSubscriptionMessage(sub_msg);
    if (json_message.empty()) {
        spdlog::error("Failed to serialize SubscriptionMessage");
        return false;
    }
    
    return sendJsonMessage(json_message);
}

bool TcpBusClient::sendJsonMessage(const std::string& json_message) {
    if (!connected_.load()) {
        return false;
    }
    
    auto conn = tcp_client_->connection();
    if (!conn || !conn->connected()) {
        return false;
    }
    
    try {
        // 使用协议打包消息
        auto packed_message = zexuan::utils::MessageProtocol::packMessage(json_message);
        if (packed_message.empty()) {
            spdlog::error("Failed to pack message");
            return false;
        }
        
        // 发送打包后的消息
        conn->send(packed_message.data(), packed_message.size());
        return true;
        
    } catch (const std::exception& e) {
        spdlog::error("TcpBusClient {} failed to send message: {}", client_name_, e.what());
        return false;
    }
}

std::string TcpBusClient::getMessageCategory(const std::string& json_str) {
    try {
        nlohmann::json json = nlohmann::json::parse(json_str);
        
        // 检查是否有msg_category字段
        if (json.contains("msg_category")) {
            return json["msg_category"].get<std::string>();
        }
        
        // 根据字段推断消息类型
        if (json.contains("type") && json.contains("source_id") && json.contains("target_id")) {
            return "common";
        }
        
        if (json.contains("event_type") && json.contains("device_uuid")) {
            return "event";
        }
        
        if (json.contains("action") && json.contains("success")) {
            return "control";
        }
        
        return "unknown";
        
    } catch (const std::exception&) {
        return "unknown";
    }
}

} // namespace bus
} // namespace zexuan
