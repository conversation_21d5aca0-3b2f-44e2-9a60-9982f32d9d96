/**
 * @file rename_client_app.hpp
 * @brief 重命名客户端应用程序主类定义
 * <AUTHOR>
 * @date 2025-08-26
 */

#ifndef RENAME_CLIENT_APP_HPP
#define RENAME_CLIENT_APP_HPP

#include "../../include/base_client_app.hpp"
#include "rename_processor.hpp"
#include <spdlog/spdlog.h>

namespace rename_client {

/**
 * @brief 重命名客户端应用程序主类
 *
 * 继承自BaseClientApp，实现具体的多线程文件重命名客户端功能
 */
class RenameClientApp : public devices::BaseClientApp {
public:
    /**
     * @brief 构造函数
     * @param event_loop 事件循环引用
     * @param server_host 服务器地址
     * @param server_port 服务器端口
     * @param client_name 客户端名称
     * @param client_id 客户端ID
     * @param thread_pool_size 线程池大小，默认32
     */
    RenameClientApp(zexuan::net::EventLoop& event_loop,
                    const std::string& server_host,
                    uint16_t server_port,
                    const std::string& client_name,
                    int client_id = 10,
                    size_t thread_pool_size = 32)
        : devices::BaseClientApp(event_loop, server_host, server_port, client_name, client_id),
          thread_pool_size_(thread_pool_size) {}

    /**
     * @brief 析构函数
     */
    ~RenameClientApp() override {
        // 确保处理器完成所有任务
        if (rename_processor_) {
            rename_processor_->waitForAllTasks();
        }
    }

protected:
    /**
     * @brief 初始化处理器和上报器
     * @return true表示初始化成功，false表示初始化失败
     */
    bool initialize() override {
        spdlog::info("Initializing rename client with thread pool size: {}", thread_pool_size_);
        
        // 初始化重命名消息处理器
        rename_processor_ = std::make_unique<RenameProcessor>(getClient(), getEventLoop(), thread_pool_size_);
        addProcessor(std::unique_ptr<devices::BaseMessageProcessor>(rename_processor_.release()));

        spdlog::info("Initialized rename client processors");
        return true;
    }

    /**
     * @brief 清理资源
     */
    void cleanup() override {
        spdlog::info("Cleaning up rename client resources...");
        
        // 等待所有重命名任务完成
        if (rename_processor_) {
            spdlog::info("Waiting for all rename tasks to complete...");
            rename_processor_->waitForAllTasks();
            spdlog::info("All rename tasks completed");
        }
        
        spdlog::info("Rename client cleanup completed");
    }

    /**
     * @brief 获取订阅的消息类型
     * @return 消息类型列表
     */
    std::vector<int> getSubscribedMessageTypes() const override {
        // 订阅文件重命名消息类型
        return {0x01};  // TYP = 0x01 表示COMMAND类型消息
    }

    /**
     * @brief 获取订阅的事件类型
     * @return 事件类型列表
     */
    std::vector<int> getSubscribedEventTypes() const override {
        // 订阅文件操作相关事件
        return {1, 2};  // 事件类型1和2
    }

    /**
     * @brief 处理业务消息（重写以添加特定的重命名逻辑）
     * @param original_message 原始消息
     */
    void processBusinessMessage(const zexuan::base::CommonMessage& original_message) override {
        spdlog::debug("RenameClientApp processing business message: invoke_id={}", original_message.invoke_id);

        // 直接从数据中提取目录路径（来自协议转换的重命名请求）
        std::string directory_path(original_message.data.begin(), original_message.data.end());
        spdlog::info("Received rename request for directory: '{}'", directory_path);

        // 创建一个模拟的Message对象来兼容现有的处理器
        zexuan::base::Message input_msg;
        input_msg.setTyp(0x05);      // Type 5 = 协议层重命名消息类型
        input_msg.setVsq(0x81);      // 可变结构限定词
        input_msg.setCot(0x06);      // 传送原因（激活）
        input_msg.setSource(0x01);   // 源地址
        input_msg.setTarget(10);     // 目标地址
        input_msg.setFun(0x01);      // 功能类型（文件重命名）
        input_msg.setInf(0x01);      // 信息序号
        input_msg.setTextContent(directory_path);

        // 查找重命名处理器并处理
        bool processed = false;
        for (auto& processor : processors_) {
            if (processor->canProcess(input_msg.getTyp())) {
                spdlog::debug("Using processor: {}", processor->getName());
                processed = processor->processMessage(original_message, input_msg);
                break;
            }
        }

        if (!processed) {
            spdlog::error("No suitable processor found for rename message");
            sendSimpleResponse(original_message);
        }
    }

private:
    /**
     * @brief 发送简单响应（重写以提供更好的错误信息）
     */
    void sendSimpleResponse(const zexuan::base::CommonMessage& original_message) {
        spdlog::debug("Sending simple response for rename client");

        zexuan::base::CommonMessage response;
        response.type = zexuan::base::MessageType::RESULT;
        response.source_id = getClient().getClientId();
        response.target_id = original_message.source_id;
        response.invoke_id = original_message.invoke_id;
        response.b_lastmsg = true;

        // 生成重命名客户端特定的响应
        std::string simple_response = "RENAME_CLIENT_RESPONSE_FOR_" + original_message.invoke_id;
        response.data.assign(simple_response.begin(), simple_response.end());

        // 发送响应到总线
        if (getClient().sendCommonMessage(response)) {
            spdlog::debug("Sent simple response to bus: invoke_id={}", response.invoke_id);
        } else {
            spdlog::error("Failed to send simple response to bus: invoke_id={}", response.invoke_id);
        }
    }

private:
    size_t thread_pool_size_;                                      ///< 线程池大小
    std::unique_ptr<RenameProcessor> rename_processor_;           ///< 重命名处理器指针（用于清理）
};

} // namespace rename_client

#endif // RENAME_CLIENT_APP_HPP
